<template>
  <div class="map-model">
    <MapBox @onMapMounted="onMapMounted" />

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
    </div>
  </div>
</template>

<script lang="jsx">
  import mapboxgl from 'mapbox-gl'
  import { boundFlatLngLats } from '@/utils/mapBounds'
  import MapBox from '@/components/MapBox'
  import poiImg from '@/assets/images/poi-marker-default.png'
  import defaultImg from '../default-project.jpg'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'MapModel',
    props: ['list', 'total', 'pageNum', 'pageSize', 'loading'],
    components: { MapBox, MapStyle },
    data() {
      return {
        mapIns: null,
        geojson84: {},
        pointLayerIns: null,
        sourceIns: null,
        markersIns: [],
        hasMarkers: false,
        hasDetailLayer: false,
      }
    },
    created() {},
    mounted() {},
    computed: {},
    watch: {
      list: {
        handler(newVal, oldVal) {
          this.markersIns.forEach(ele => {
            ele.remove()
          })

          if (this.mapIns?.getSource('points')) {
            this.mapIns.off('zoomend', this.onMapZoomEnd)
            clearSourceAndLayer(this.mapIns, ['points'], ['points'])
          }

          if (this.mapIns?.getSource('points-detail')) {
            clearSourceAndLayer(this.mapIns, ['points-detail'], ['points-detail'])
          }

          this.mapBounds(newVal)
        },
        deep: true,
      },
    },
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.mapBounds(this.list)
      },

      setMarkers(list) {
        this.geojson84 = {
          type: 'FeatureCollection',
          features: list
            .filter(el => +el.longitude && +el.latitude)
            .map(item => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+item.longitude, +item.latitude],
                },
                properties: {
                  ...item,
                  name: item.projectName,
                },
              }
            }),
        }
        this.mapIns.loadImage(poiImg, (error, image) => {
          if (!image) return
          this.mapIns.addImage('custom-marker', image)

          this.mapIns.addSource('points', { type: 'geojson', data: this.geojson84 })

          this.mapIns
            .addLayer({
              id: 'points',
              type: 'circle',
              source: 'points',
              paint: {
                'circle-color': '#165DFF',
                'circle-radius': ['interpolate', ['exponential', 1], ['zoom'], 5, 3, 13.99, 6, 14, 0],
                'circle-stroke-width': 0,
                'circle-stroke-color': '#ffffff',
              },
            })
            .on('click', 'points', e => {
              const currentInfo = e.features[0].properties
              this.$listeners.clickItem && this.$listeners.clickItem(currentInfo)
            })

          // this.mapIns
          //   .addLayer({
          //     id: 'points2',
          //     type: 'symbol',
          //     source: 'points',
          //     layout: {
          //       'icon-image': 'custom-marker',
          //       'icon-ignore-placement': true,
          //       'icon-allow-overlap': true,
          //       'text-size': ['interpolate', ['exponential', 8], ['zoom'], 5, 0, 13.99, 0, 14, 12, 14.99, 12, 15, 0],
          //       'text-field': ['get', 'name'],
          //       'text-offset': [0, 1.25],
          //       'text-anchor': 'top',
          //       'text-allow-overlap': true,
          //       'icon-size': ['interpolate', ['exponential', 5], ['zoom'], 5, 0, 13.99, 0, 14, 0.4, 14.99, 0.5, 15, 0]
          //     }
          //   })
          //   .on('click', 'points2', e => {
          //     const currentInfo = e.features[0].properties
          //     this.$listeners.clickItem && this.$listeners.clickItem(currentInfo)
          //   })

          this.mapIns.on('zoomend', this.onMapZoomEnd)
        })
      },

      onMapZoomEnd(e) {
        if (e.target.getZoom() >= 14) {
          if (this.hasDetailLayer) return

          // 使用GeoJSON图层替代DOM Marker，提升性能
          this.mapIns.addSource('points-detail', {
            type: 'geojson',
            data: this.geojson84
          })

          // 添加详细图标图层
          this.mapIns.addLayer({
            id: 'points-detail',
            type: 'symbol',
            source: 'points-detail',
            layout: {
              'icon-image': 'custom-marker',
              'icon-size': 0.8,
              'icon-allow-overlap': true,
              'icon-ignore-placement': true,
              'text-field': ['get', 'name'],
              'text-font': ['Open Sans Regular'],
              'text-offset': [2, 0],
              'text-anchor': 'left',
              'text-size': 14,
              'text-allow-overlap': true
            },
            paint: {
              'text-color': '#1D2129',
              'text-halo-color': '#ffffff',
              'text-halo-width': 2
            }
          })

          // 添加点击事件
          this.mapIns.on('click', 'points-detail', (e) => {
            const currentInfo = e.features[0].properties
            this.$listeners.clickItem && this.$listeners.clickItem(currentInfo)
          })

          // 添加鼠标悬停效果
          this.mapIns.on('mouseenter', 'points-detail', () => {
            this.mapIns.getCanvas().style.cursor = 'pointer'
          })

          this.mapIns.on('mouseleave', 'points-detail', () => {
            this.mapIns.getCanvas().style.cursor = ''
          })

          this.hasDetailLayer = true
        } else {
          if (this.hasDetailLayer && this.mapIns?.getSource('points-detail')) {
            clearSourceAndLayer(this.mapIns, ['points-detail'], ['points-detail'])
            this.hasDetailLayer = false
          }
        }
      },

      mapBounds(newList) {
        if (newList?.length && !!this.mapIns) {
          const arr = newList
            .filter(el => +el.longitude && +el.latitude)
            .map(el => [el.longitude, el.latitude])

          if (newList?.length == 1) {
            this.mapIns.flyTo({
              center: arr?.[0],
              zoom: 13,
            })
          } else {
            // 地图自动缩放偏移
            boundFlatLngLats(arr, this.mapIns)
          }

          this.setMarkers(newList)
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .map-model {
    margin: 0 16px 16px;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
  }
</style>
